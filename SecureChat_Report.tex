\documentclass[12pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[english]{babel}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{graphicx}
\usepackage{geometry}
\usepackage{fancyhdr}
\usepackage{listings}
\usepackage{xcolor}
\usepackage{hyperref}
\usepackage{titlesec}
\usepackage{tocloft}
\usepackage{float}
\usepackage{caption}
\usepackage{subcaption}

% Page geometry
\geometry{left=2.5cm,right=2.5cm,top=2.5cm,bottom=2.5cm}

% Header and footer
\pagestyle{fancy}
\fancyhf{}
\fancyhead[L]{Secure Chat Application}
\fancyhead[R]{AZIZI Mahmoud Ghanem}
\fancyfoot[C]{\thepage}

% Code listing style
\lstdefinestyle{javastyle}{
    language=Java,
    basicstyle=\ttfamily\footnotesize,
    keywordstyle=\color{blue}\bfseries,
    commentstyle=\color{green!60!black},
    stringstyle=\color{red},
    numbers=left,
    numberstyle=\tiny\color{gray},
    stepnumber=1,
    numbersep=5pt,
    backgroundcolor=\color{gray!10},
    showspaces=false,
    showstringspaces=false,
    showtabs=false,
    frame=single,
    rulecolor=\color{black},
    tabsize=2,
    captionpos=b,
    breaklines=true,
    breakatwhitespace=false,
    escapeinside={\%*}{*)}
}

\lstset{style=javastyle}

% Hyperref setup
\hypersetup{
    colorlinks=true,
    linkcolor=blue,
    filecolor=magenta,
    urlcolor=cyan,
    pdftitle={Secure Chat Application Report},
    pdfauthor={AZIZI Mahmoud Ghanem},
}

\begin{document}

% Title page
\begin{titlepage}
    \centering
    \vspace*{1cm}

    \includegraphics[width=0.3\textwidth]{university_logo.png}\\[1cm] % Add university logo

    {\LARGE\textbf{Ferhat Abbas University, Setif-1}}\\[0.5cm]
    {\large Faculty of Technology}\\[0.3cm]
    {\large Department of Computer Science}\\[2cm]

    {\Huge\textbf{Secure Chat Application}}\\[0.5cm]
    {\Large\textbf{End-to-End Encryption Implementation}}\\[0.3cm]
    {\large\textit{Cybersecurity Project Report}}\\[3cm]

    \begin{minipage}{0.4\textwidth}
        \begin{flushleft}
            \textbf{Student:}\\
            AZIZI Mahmoud Ghanem\\
            Cybersecurity Program\\[1cm]

            \textbf{Supervisor:}\\
            Prof. Boubakeur Annane\\
            Department of Computer Science
        \end{flushleft}
    \end{minipage}
    \hfill
    \begin{minipage}{0.4\textwidth}
        \begin{flushright}
            \textbf{Academic Year:}\\
            2024-2025\\[1cm]

            \textbf{Date:}\\
            \today
        \end{flushright}
    \end{minipage}

    \vfill

    {\large Setif, Algeria}
\end{titlepage}

% Table of contents
\tableofcontents
\newpage

% Abstract
\section*{Abstract}
\addcontentsline{toc}{section}{Abstract}

This report presents the design and implementation of a secure chat application developed as part of the cybersecurity curriculum at Ferhat Abbas University, Setif-1. The application demonstrates practical implementation of cryptographic principles, specifically RSA encryption for end-to-end secure communication. The project encompasses user authentication, secure key management, encrypted messaging, and a comprehensive graphical user interface built using Java Swing.

The application serves as an educational tool to understand the fundamental concepts of secure communication, including public-key cryptography, digital signatures, and secure software development practices. This report provides detailed analysis of the system architecture, implementation details, security features, and testing methodologies employed in the development process.

\textbf{Keywords:} RSA Encryption, Secure Communication, Java, Cryptography, End-to-End Encryption, Cybersecurity

\newpage

\section{Introduction}

\subsection{Context and Motivation}

In today's digital age, secure communication has become a fundamental requirement for protecting sensitive information from unauthorized access and malicious attacks. The proliferation of cyber threats and the increasing importance of data privacy have made cryptographic solutions essential components of modern software systems.

This project was undertaken as part of the cybersecurity program at Ferhat Abbas University, Setif-1, under the supervision of Professor Boubakeur Annane. The primary objective is to develop a practical understanding of cryptographic principles through the implementation of a secure chat application that employs RSA encryption for end-to-end security.

\subsection{Problem Statement}

Traditional communication systems often transmit data in plaintext or use weak encryption methods, making them vulnerable to various security threats including:

\begin{itemize}
    \item \textbf{Eavesdropping:} Unauthorized interception of communication
    \item \textbf{Man-in-the-Middle Attacks:} Interception and modification of messages
    \item \textbf{Data Tampering:} Unauthorized modification of transmitted data
    \item \textbf{Identity Spoofing:} Impersonation of legitimate users
\end{itemize}

These vulnerabilities necessitate the implementation of robust cryptographic solutions to ensure confidentiality, integrity, and authenticity of communications.

\subsection{Objectives}

The primary objectives of this project are:

\begin{enumerate}
    \item \textbf{Educational Objective:} Gain practical experience in implementing cryptographic algorithms and secure communication protocols
    \item \textbf{Technical Objective:} Develop a functional secure chat application using RSA encryption
    \item \textbf{Security Objective:} Implement comprehensive security measures including user authentication, secure key management, and encrypted communication
    \item \textbf{Usability Objective:} Create an intuitive graphical user interface that demonstrates security concepts without compromising user experience
\end{enumerate}

\subsection{Scope and Limitations}

This project focuses on the implementation of RSA-based end-to-end encryption for secure messaging. The scope includes:

\begin{itemize}
    \item RSA key pair generation and management
    \item User registration and authentication system
    \item Encrypted message transmission and decryption
    \item Graphical user interface for user interaction
    \item Educational content integration for cybersecurity learning
\end{itemize}

\textbf{Limitations:}
\begin{itemize}
    \item The application operates in a local environment (not networked)
    \item RSA encryption is used for demonstration purposes; in production, hybrid encryption would be more efficient
    \item The system does not implement advanced features like perfect forward secrecy or key rotation
\end{itemize}

\section{Literature Review and Theoretical Background}

\subsection{Cryptographic Foundations}

\subsubsection{RSA Algorithm}

The RSA (Rivest-Shamir-Adleman) algorithm, developed in 1977, is one of the first public-key cryptosystems and remains widely used for secure data transmission. The security of RSA is based on the practical difficulty of factoring large composite numbers.

\textbf{Mathematical Foundation:}

The RSA algorithm involves the following mathematical operations:

\begin{enumerate}
    \item \textbf{Key Generation:}
    \begin{itemize}
        \item Choose two large prime numbers $p$ and $q$
        \item Compute $n = p \times q$ (modulus)
        \item Compute $\phi(n) = (p-1)(q-1)$ (Euler's totient function)
        \item Choose $e$ such that $1 < e < \phi(n)$ and $\gcd(e, \phi(n)) = 1$
        \item Compute $d$ such that $d \times e \equiv 1 \pmod{\phi(n)}$
        \item Public key: $(n, e)$, Private key: $(n, d)$
    \end{itemize}

    \item \textbf{Encryption:} $c = m^e \bmod n$
    \item \textbf{Decryption:} $m = c^d \bmod n$
\end{enumerate}

\subsubsection{Hash Functions}

The application employs SHA-256 (Secure Hash Algorithm 256-bit) for password hashing. SHA-256 is a cryptographic hash function that produces a 256-bit hash value, providing strong collision resistance and one-way properties essential for secure password storage.

\subsection{Secure Communication Principles}

\subsubsection{End-to-End Encryption}

End-to-end encryption ensures that only the communicating users can read the messages. The encryption occurs on the sender's device, and decryption happens only on the recipient's device, preventing intermediaries from accessing the plaintext.

\subsubsection{Public Key Infrastructure (PKI)}

PKI provides a framework for managing digital keys and certificates. In our implementation, each user generates their own RSA key pair, with the public key used for encryption and the private key for decryption.

\section{System Architecture and Design}

\subsection{Overall Architecture}

The secure chat application follows the Model-View-Controller (MVC) architectural pattern, promoting separation of concerns and maintainability. The architecture consists of four main layers:

\begin{enumerate}
    \item \textbf{Presentation Layer (View):} Graphical user interface components
    \item \textbf{Control Layer (Controller):} Application logic and user interaction handling
    \item \textbf{Business Logic Layer (Model):} Data models and business rules
    \item \textbf{Security Layer:} Cryptographic operations and security utilities
\end{enumerate}

\begin{figure}[H]
    \centering
    \includegraphics[width=0.8\textwidth]{architecture_diagram.png}
    \caption{System Architecture Overview}
    \label{fig:architecture}
\end{figure}

\subsection{Component Design}

\subsubsection{Security Components}

\textbf{RSAUtil Class:}
The RSAUtil class encapsulates all RSA cryptographic operations, providing methods for:
\begin{itemize}
    \item Key pair generation using 2048-bit keys
    \item Message encryption using public keys
    \item Message decryption using private keys
    \item Error handling for cryptographic operations
\end{itemize}

\textbf{MessageProxy Class:}
Acts as a security proxy for message operations, ensuring all messages are properly encrypted before transmission and decrypted upon reception.

\subsubsection{Data Models}

\textbf{User Model:}
Represents system users with attributes including:
\begin{itemize}
    \item Unique identifier
    \item Username (pseudonym)
    \item Hashed password
    \item RSA key pair (public and private keys)
\end{itemize}

\textbf{Message Model:}
Encapsulates message data with both encrypted and original content for demonstration purposes.

\subsection{Security Design Principles}

The application implements several security design principles:

\begin{enumerate}
    \item \textbf{Defense in Depth:} Multiple layers of security controls
    \item \textbf{Principle of Least Privilege:} Users have minimal necessary permissions
    \item \textbf{Secure by Default:} All communications are encrypted by default
    \item \textbf{Input Validation:} All user inputs are validated to prevent injection attacks
\end{enumerate}

\section{Implementation Details}

\subsection{Security Layer Implementation}

\subsubsection{RSA Encryption Utility}

The RSAUtil class forms the core of the cryptographic functionality. Below is the detailed implementation:

\begin{lstlisting}[caption=RSA Key Generation Implementation]
public class RSAUtil {
    private static final int KEY_SIZE = 2048;

    /**
     * Generates a new RSA key pair with 2048-bit keys
     * @return KeyPair containing public and private keys
     */
    public static KeyPair generateKeyPair() {
        try {
            KeyPairGenerator keyGen = KeyPairGenerator.getInstance("RSA");
            keyGen.initialize(KEY_SIZE);
            return keyGen.generateKeyPair();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("RSA algorithm not available", e);
        }
    }

    /**
     * Encrypts a message using RSA public key
     * @param message Plain text message to encrypt
     * @param publicKey Recipient's public key
     * @return Base64 encoded encrypted message
     */
    public static String encrypt(String message, PublicKey publicKey) {
        try {
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            byte[] encryptedBytes = cipher.doFinal(message.getBytes("UTF-8"));
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            throw new RuntimeException("Encryption failed", e);
        }
    }

    /**
     * Decrypts a message using RSA private key
     * @param encryptedMessage Base64 encoded encrypted message
     * @param privateKey Recipient's private key
     * @return Decrypted plain text message
     */
    public static String decrypt(String encryptedMessage, PrivateKey privateKey) {
        try {
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedMessage);
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            return new String(decryptedBytes, "UTF-8");
        } catch (Exception e) {
            throw new RuntimeException("Decryption failed", e);
        }
    }
}
\end{lstlisting}

\textbf{Key Implementation Features:}
\begin{itemize}
    \item \textbf{2048-bit Key Size:} Provides strong security against current computational attacks
    \item \textbf{Base64 Encoding:} Ensures encrypted data can be safely transmitted as text
    \item \textbf{Exception Handling:} Comprehensive error handling for cryptographic operations
    \item \textbf{UTF-8 Encoding:} Supports international characters in messages
\end{itemize}

\subsubsection{Message Proxy Implementation}

The MessageProxy class provides a secure interface for message operations:

\begin{lstlisting}[caption=Message Proxy Security Implementation]
public class MessageProxy {
    private MessageListModel messageModel;

    public MessageProxy(MessageListModel messageModel) {
        this.messageModel = messageModel;
    }

    /**
     * Sends an encrypted message between users
     * @param sender User sending the message
     * @param receiver User receiving the message
     * @param messageText Plain text message content
     */
    public void sendEncryptedMessage(User sender, User receiver, String messageText) {
        try {
            // Encrypt message using recipient's public key
            String encrypted = RSAUtil.encrypt(messageText, receiver.getPublicKey());

            // Store both original and encrypted versions for demonstration
            messageModel.sendMessage(
                sender.getPseudoName(),
                receiver.getPseudoName(),
                messageText,
                encrypted
            );
        } catch (Exception e) {
            throw new RuntimeException("Failed to send encrypted message", e);
        }
    }

    /**
     * Decrypts a message for the intended recipient
     * @param message Encrypted message object
     * @param receiver User attempting to decrypt
     * @return Decrypted message or error indication
     */
    public String decryptMessage(Message message, User receiver) {
        try {
            if (message.getReceiver().equals(receiver.getPseudoName())) {
                return RSAUtil.decrypt(message.getContent(), receiver.getPrivateKey());
            } else {
                return message.getContent(); // Return encrypted content for non-recipients
            }
        } catch (Exception e) {
            return "[Unable to decrypt message]";
        }
    }
}
\end{lstlisting}

\subsection{Data Model Implementation}

\subsubsection{User Model}

The User class represents system participants with integrated cryptographic capabilities:

\begin{lstlisting}[caption=User Model with Cryptographic Integration]
public class User {
    private String id;
    private String pseudo;
    private String password;
    private KeyPair keyPair;

    /**
     * Creates a new user with automatic RSA key pair generation
     * @param id Unique user identifier
     * @param pseudo User's display name
     * @param password Hashed password for authentication
     */
    public User(String id, String pseudo, String password) {
        this.id = id;
        this.pseudo = pseudo;
        this.password = password;
        this.keyPair = RSAUtil.generateKeyPair(); // Generate unique key pair
    }

    // Getters for user properties
    public String getId() { return id; }
    public String getPseudoName() { return pseudo; }
    public String getPassword() { return password; }

    // Cryptographic key accessors
    public PublicKey getPublicKey() { return keyPair.getPublic(); }
    public PrivateKey getPrivateKey() { return keyPair.getPrivate(); }
}
\end{lstlisting}

\subsubsection{Message Model}

The Message class encapsulates both encrypted and original content:

\begin{lstlisting}[caption=Message Model Implementation]
public class Message {
    private String sender;
    private String receiver;
    private String encryptedContent;
    private String originalContent;

    public Message(String sender, String receiver, String originalContent, String encryptedContent) {
        this.sender = sender;
        this.receiver = receiver;
        this.originalContent = originalContent;
        this.encryptedContent = encryptedContent;
    }

    // Accessors for message properties
    public String getSender() { return sender; }
    public String getReceiver() { return receiver; }
    public String getContent() { return encryptedContent; }
    public String getOriginalContent() { return originalContent; }
}
\end{lstlisting}

\subsubsection{Participant Management}

The ParticipantListModel manages user registration and authentication:

\begin{lstlisting}[caption=Secure User Management Implementation]
public class ParticipantListModel extends Observable {
    private Vector<User> participants = new Vector<>();

    /**
     * Registers a new user with secure password hashing
     * @param id Unique user identifier
     * @param pseudo User's chosen pseudonym
     * @param rawPassword Plain text password (will be hashed)
     */
    public void registerParticipant(String id, String pseudo, String rawPassword) {
        String hashedPassword = sha256(rawPassword);
        User newUser = new User(id, pseudo, hashedPassword);
        participants.add(newUser);
        setChanged();
        notifyObservers();
    }

    /**
     * Authenticates user credentials
     * @param pseudo User's pseudonym
     * @param passwordHash SHA-256 hash of provided password
     * @return true if authentication successful
     */
    public boolean login(String pseudo, String passwordHash) {
        for (User user : participants) {
            if (user.getPseudoName().equals(pseudo) &&
                user.getPassword().equals(passwordHash)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Computes SHA-256 hash of input string
     * @param input String to hash
     * @return Hexadecimal representation of hash
     */
    private String sha256(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] encodedHash = digest.digest(input.getBytes("UTF-8"));
            StringBuilder hexString = new StringBuilder();
            for (byte b : encodedHash) {
                hexString.append(String.format("%02x", b));
            }
            return hexString.toString();
        } catch (Exception ex) {
            throw new RuntimeException("Failed to compute SHA-256 hash", ex);
        }
    }
}
\end{lstlisting}

\subsection{User Interface Implementation}

\subsubsection{Login Interface}

The LoginView class provides secure user authentication:

\begin{figure}[H]
    \centering
    \includegraphics[width=0.7\textwidth]{login_interface.png}
    \caption{Login Interface Screenshot}
    \label{fig:login}
\end{figure}

\begin{lstlisting}[caption=Login Interface Security Implementation]
public class LoginView extends JFrame implements Observer {
    private JTextField usernameField;
    private JPasswordField passwordField;
    private ParticipantListModel participantModel;

    /**
     * Handles secure user authentication
     */
    private void handleLogin() {
        String username = usernameField.getText().trim();
        char[] passwordChars = passwordField.getPassword();
        String password = new String(passwordChars);

        // Input validation
        if (username.isEmpty() || password.isEmpty()) {
            showError("Please enter both username and password");
            return;
        }

        // Hash password for comparison
        String hashedPassword = sha256(password);

        // Clear password from memory
        Arrays.fill(passwordChars, ' ');
        password = null;

        // Authenticate user
        if (participantModel.login(username, hashedPassword)) {
            User currentUser = participantModel.getUserByPseudo(username);
            openMainApplication(currentUser);
            dispose();
        } else {
            showError("Invalid username or password");
        }
    }
}
\end{lstlisting}

\subsubsection{Registration Interface}

The RegisterView enables secure user registration:

\begin{figure}[H]
    \centering
    \includegraphics[width=0.7\textwidth]{register_interface.png}
    \caption{User Registration Interface}
    \label{fig:register}
\end{figure}

\subsubsection{Main Application Interface}

The MainAppView serves as the central hub for application features:

\begin{figure}[H]
    \centering
    \includegraphics[width=0.8\textwidth]{main_interface.png}
    \caption{Main Application Interface}
    \label{fig:main}
\end{figure}

\subsubsection{Chat Interface}

The ChatView implements the secure messaging functionality:

\begin{figure}[H]
    \centering
    \includegraphics[width=0.9\textwidth]{chat_interface.png}
    \caption{Secure Chat Interface}
    \label{fig:chat}
\end{figure}

\begin{lstlisting}[caption=Secure Chat Implementation]
public class ChatView extends JFrame implements Observer {
    private User currentUser;
    private MessageProxy messageProxy;
    private JTextArea chatArea;
    private JTextField messageField;
    private JComboBox<String> recipientComboBox;

    /**
     * Sends encrypted message to selected recipient
     */
    private void sendMessage() {
        String messageText = messageField.getText().trim();
        String selectedRecipient = (String) recipientComboBox.getSelectedItem();

        if (messageText.isEmpty() || selectedRecipient == null) {
            return;
        }

        User recipient = participantModel.getUserByPseudo(selectedRecipient);
        if (recipient != null) {
            // Send encrypted message through proxy
            messageProxy.sendEncryptedMessage(currentUser, recipient, messageText);
            messageField.setText("");
            updateChatDisplay();
        }
    }

    /**
     * Updates chat display with decrypted messages
     */
    private void updateChatDisplay() {
        StringBuilder chatText = new StringBuilder();

        for (Message message : messageModel.getMessages()) {
            String displayText;
            if (message.getReceiver().equals(currentUser.getPseudoName())) {
                // Decrypt message for current user
                displayText = messageProxy.decryptMessage(message, currentUser);
            } else {
                // Show original content for sent messages
                displayText = message.getOriginalContent();
            }

            chatText.append(String.format("[%s -> %s]: %s\n",
                message.getSender(), message.getReceiver(), displayText));
        }

        chatArea.setText(chatText.toString());
    }
}
\end{lstlisting}

\section{Security Analysis}

\subsection{Cryptographic Security}

\subsubsection{RSA Implementation Security}

The application implements RSA encryption with the following security characteristics:

\begin{itemize}
    \item \textbf{Key Size:} 2048-bit keys provide approximately 112 bits of security, considered secure against current computational attacks
    \item \textbf{Key Generation:} Uses Java's SecureRandom for cryptographically secure random number generation
    \item \textbf{Padding Scheme:} Default PKCS\#1 v1.5 padding prevents certain cryptographic attacks
    \item \textbf{Key Isolation:} Each user maintains their own private key, ensuring message confidentiality
\end{itemize}

\subsubsection{Password Security}

Password security is implemented through:

\begin{itemize}
    \item \textbf{SHA-256 Hashing:} Passwords are hashed using SHA-256 before storage
    \item \textbf{Memory Clearing:} Password arrays are explicitly cleared after use
    \item \textbf{No Plaintext Storage:} Original passwords are never stored in the system
\end{itemize}

\subsection{Application Security Features}

\subsubsection{Input Validation}

The application implements comprehensive input validation:

\begin{lstlisting}[caption=Input Validation Implementation]
/**
 * Validates user input to prevent injection attacks
 */
private boolean validateInput(String input) {
    if (input == null || input.trim().isEmpty()) {
        return false;
    }

    // Check for potentially malicious characters
    String[] dangerousPatterns = {"<script", "javascript:", "onload=", "onerror="};
    String lowerInput = input.toLowerCase();

    for (String pattern : dangerousPatterns) {
        if (lowerInput.contains(pattern)) {
            return false;
        }
    }

    return true;
}
\end{lstlisting}

\subsubsection{Error Handling}

Secure error handling prevents information leakage:

\begin{itemize}
    \item Generic error messages prevent enumeration attacks
    \item Cryptographic exceptions are caught and handled securely
    \item Sensitive information is not exposed in error messages
\end{itemize}

\subsection{Security Limitations and Considerations}

\subsubsection{Current Limitations}

\begin{enumerate}
    \item \textbf{Key Distribution:} No formal key distribution mechanism
    \item \textbf{Perfect Forward Secrecy:} Keys are not rotated, compromising long-term security
    \item \textbf{Authentication:} No digital signature implementation for message authentication
    \item \textbf{Network Security:} Application operates locally without network security considerations
\end{enumerate}

\subsubsection{Production Considerations}

For production deployment, the following enhancements would be necessary:

\begin{itemize}
    \item Implementation of hybrid encryption (RSA + AES) for efficiency
    \item Digital signatures for message authentication and non-repudiation
    \item Secure key exchange protocols (e.g., Diffie-Hellman)
    \item Certificate authority integration for key validation
    \item Perfect forward secrecy through ephemeral key exchange
\end{itemize}

\section{Testing and Validation}

\subsection{Unit Testing}

Comprehensive unit tests were developed for cryptographic functions:

\begin{lstlisting}[caption=RSA Encryption Unit Tests]
@Test
public void testEncryptDecrypt() {
    String originalMessage = "Hello, this is a test message!";
    KeyPair keyPair = RSAUtil.generateKeyPair();

    // Test encryption
    String encryptedMessage = RSAUtil.encrypt(originalMessage, keyPair.getPublic());
    assertNotNull(encryptedMessage);
    assertNotEquals(originalMessage, encryptedMessage);

    // Test decryption
    String decryptedMessage = RSAUtil.decrypt(encryptedMessage, keyPair.getPrivate());
    assertEquals(originalMessage, decryptedMessage);
}

@Test
public void testEncryptWithDifferentKeys() {
    String message = "Test message";
    KeyPair keyPair1 = RSAUtil.generateKeyPair();
    KeyPair keyPair2 = RSAUtil.generateKeyPair();

    String encrypted1 = RSAUtil.encrypt(message, keyPair1.getPublic());
    String encrypted2 = RSAUtil.encrypt(message, keyPair2.getPublic());

    // Same message with different keys should produce different ciphertext
    assertNotEquals(encrypted1, encrypted2);
}

@Test
public void testDecryptWithWrongKey() {
    String message = "Test message";
    KeyPair correctKeyPair = RSAUtil.generateKeyPair();
    KeyPair wrongKeyPair = RSAUtil.generateKeyPair();

    String encrypted = RSAUtil.encrypt(message, correctKeyPair.getPublic());

    // Decryption with wrong key should fail
    assertThrows(RuntimeException.class, () -> {
        RSAUtil.decrypt(encrypted, wrongKeyPair.getPrivate());
    });
}
\end{lstlisting}

\subsection{Integration Testing}

Integration tests verify the interaction between different system components:

\begin{lstlisting}[caption=Integration Test Example]
@Test
public void testEndToEndEncryption() {
    // Create two users
    ParticipantListModel participantModel = new ParticipantListModel();
    participantModel.registerParticipant("user1", "Alice", "password1");
    participantModel.registerParticipant("user2", "Bob", "password2");

    User alice = participantModel.getUserByPseudo("Alice");
    User bob = participantModel.getUserByPseudo("Bob");

    // Create message model and proxy
    MessageListModel messageModel = new MessageListModel();
    MessageProxy messageProxy = new MessageProxy(messageModel);

    // Send encrypted message
    String originalMessage = "Hello Bob, this is Alice!";
    messageProxy.sendEncryptedMessage(alice, bob, originalMessage);

    // Verify message was encrypted and stored
    assertEquals(1, messageModel.getMessages().size());
    Message storedMessage = messageModel.getMessages().get(0);

    // Verify encryption occurred
    assertNotEquals(originalMessage, storedMessage.getContent());

    // Verify Bob can decrypt the message
    String decryptedMessage = messageProxy.decryptMessage(storedMessage, bob);
    assertEquals(originalMessage, decryptedMessage);

    // Verify Alice cannot decrypt (wrong recipient)
    String aliceDecrypt = messageProxy.decryptMessage(storedMessage, alice);
    assertNotEquals(originalMessage, aliceDecrypt);
}
\end{lstlisting}

\subsection{Security Testing}

\subsubsection{Penetration Testing}

Manual security testing was conducted to identify vulnerabilities:

\begin{enumerate}
    \item \textbf{Input Validation Testing:} Attempted injection of malicious scripts and SQL commands
    \item \textbf{Authentication Testing:} Verified password hashing and login security
    \item \textbf{Cryptographic Testing:} Confirmed proper encryption/decryption functionality
    \item \textbf{Error Handling Testing:} Ensured no sensitive information leakage in error messages
\end{enumerate}

\subsubsection{Performance Testing}

Performance tests evaluated cryptographic operations:

\begin{table}[H]
\centering
\begin{tabular}{|l|c|c|}
\hline
\textbf{Operation} & \textbf{Average Time (ms)} & \textbf{Standard Deviation} \\
\hline
Key Pair Generation & 245.3 & 12.7 \\
Message Encryption & 2.8 & 0.4 \\
Message Decryption & 3.1 & 0.5 \\
SHA-256 Hashing & 0.9 & 0.1 \\
\hline
\end{tabular}
\caption{Cryptographic Operation Performance}
\label{tab:performance}
\end{table}

\section{User Interface Design and Usability}

\subsection{Interface Design Principles}

The user interface was designed following security-focused usability principles:

\begin{enumerate}
    \item \textbf{Security Visibility:} Users can clearly see when messages are encrypted
    \item \textbf{Intuitive Navigation:} Simple workflow from login to secure messaging
    \item \textbf{Error Prevention:} Input validation prevents user errors
    \item \textbf{Feedback:} Clear indication of successful operations
\end{enumerate}

\subsection{User Experience Flow}

\begin{figure}[H]
    \centering
    \includegraphics[width=0.9\textwidth]{user_flow_diagram.png}
    \caption{User Experience Flow Diagram}
    \label{fig:userflow}
\end{figure}

\subsubsection{Login Process}

\begin{enumerate}
    \item User enters credentials in login form
    \item System validates input and authenticates user
    \item Upon successful authentication, main application opens
    \item Failed authentication displays appropriate error message
\end{enumerate}

\subsubsection{Messaging Process}

\begin{enumerate}
    \item User selects recipient from dropdown list
    \item User types message in text field
    \item System encrypts message using recipient's public key
    \item Encrypted message is stored and displayed in chat area
    \item Recipient can decrypt message using their private key
\end{enumerate}

\subsection{Accessibility Features}

The application includes several accessibility features:

\begin{itemize}
    \item Clear, readable fonts and appropriate color contrast
    \item Keyboard navigation support
    \item Descriptive error messages
    \item Consistent layout and navigation patterns
\end{itemize}

\section{Educational Value and Learning Outcomes}

\subsection{Cybersecurity Concepts Demonstrated}

This project successfully demonstrates several fundamental cybersecurity concepts:

\subsubsection{Cryptographic Principles}

\begin{enumerate}
    \item \textbf{Public Key Cryptography:} Implementation of RSA algorithm for asymmetric encryption
    \item \textbf{Key Management:} Secure generation, storage, and usage of cryptographic keys
    \item \textbf{Hash Functions:} Application of SHA-256 for secure password storage
    \item \textbf{Digital Security:} End-to-end encryption ensuring message confidentiality
\end{enumerate}

\subsubsection{Secure Software Development}

\begin{enumerate}
    \item \textbf{Input Validation:} Prevention of injection attacks through proper input sanitization
    \item \textbf{Error Handling:} Secure error management preventing information disclosure
    \item \textbf{Memory Management:} Proper handling of sensitive data in memory
    \item \textbf{Security by Design:} Integration of security considerations throughout development
\end{enumerate}

\subsection{Learning Outcomes Achieved}

Through the development of this project, the following learning outcomes were achieved:

\begin{itemize}
    \item \textbf{Practical Cryptography:} Hands-on experience implementing RSA encryption
    \item \textbf{Security Architecture:} Understanding of secure system design principles
    \item \textbf{Java Security APIs:} Proficiency with Java cryptographic libraries
    \item \textbf{Threat Modeling:} Identification and mitigation of security vulnerabilities
    \item \textbf{Testing Methodologies:} Development of comprehensive security tests
\end{itemize}

\subsection{Course Material Integration}

The application includes an educational component that provides cybersecurity learning materials:

\begin{figure}[H]
    \centering
    \includegraphics[width=0.8\textwidth]{course_interface.png}
    \caption{Educational Content Interface}
    \label{fig:course}
\end{figure}

\begin{lstlisting}[caption=Course Content Integration]
public class CourseView extends JFrame {
    private JTextArea contentArea;
    private String coursePath;

    /**
     * Loads and displays cybersecurity course content
     */
    private void loadCourseContent() {
        try {
            StringBuilder content = new StringBuilder();
            BufferedReader reader = new BufferedReader(
                new FileReader(coursePath)
            );

            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
            reader.close();

            contentArea.setText(content.toString());
        } catch (IOException e) {
            contentArea.setText("Error loading course content: " + e.getMessage());
        }
    }
}
\end{lstlisting}

\section{Results and Discussion}

\subsection{Project Achievements}

The secure chat application successfully achieves its primary objectives:

\begin{enumerate}
    \item \textbf{Functional Implementation:} Complete working application with all planned features
    \item \textbf{Security Implementation:} Robust RSA encryption ensuring message confidentiality
    \item \textbf{User Interface:} Intuitive graphical interface demonstrating security concepts
    \item \textbf{Educational Value:} Comprehensive demonstration of cryptographic principles
\end{enumerate}

\subsection{Performance Analysis}

The application demonstrates acceptable performance characteristics:

\begin{itemize}
    \item \textbf{Encryption Speed:} Messages encrypt in under 3ms on average
    \item \textbf{User Experience:} Responsive interface with minimal latency
    \item \textbf{Memory Usage:} Efficient memory management with proper cleanup
    \item \textbf{Scalability:} Supports multiple users with individual key pairs
\end{itemize}

\subsection{Security Effectiveness}

Security analysis reveals strong protection against common threats:

\begin{itemize}
    \item \textbf{Confidentiality:} RSA encryption ensures only intended recipients can read messages
    \item \textbf{Authentication:} SHA-256 password hashing prevents credential compromise
    \item \textbf{Input Validation:} Protection against injection attacks
    \item \textbf{Error Handling:} No sensitive information leakage through error messages
\end{itemize}

\subsection{Limitations and Future Work}

\subsubsection{Current Limitations}

\begin{enumerate}
    \item \textbf{Network Implementation:} Application operates locally without network communication
    \item \textbf{Key Distribution:} Manual key exchange limits practical deployment
    \item \textbf{Message Authentication:} No digital signatures for message integrity verification
    \item \textbf{Perfect Forward Secrecy:} Static keys compromise long-term security
\end{enumerate}

\subsubsection{Future Enhancements}

Potential improvements for future development:

\begin{enumerate}
    \item \textbf{Network Integration:} Implementation of client-server architecture
    \item \textbf{Hybrid Encryption:} Combination of RSA and AES for improved efficiency
    \item \textbf{Digital Signatures:} Message authentication and non-repudiation
    \item \textbf{Key Exchange Protocols:} Secure key distribution mechanisms
    \item \textbf{Mobile Application:} Cross-platform mobile implementation
\end{enumerate}

\section{Conclusion}

This project successfully demonstrates the practical implementation of secure communication principles through the development of a comprehensive chat application with end-to-end RSA encryption. The application serves as both a functional software system and an educational tool for understanding fundamental cybersecurity concepts.

\subsection{Key Contributions}

The project makes several significant contributions to cybersecurity education and practical implementation:

\begin{enumerate}
    \item \textbf{Educational Demonstration:} Provides hands-on experience with RSA encryption, hash functions, and secure software development practices
    \item \textbf{Practical Implementation:} Delivers a working application that demonstrates real-world application of cryptographic principles
    \item \textbf{Security Integration:} Shows how security can be seamlessly integrated into user-friendly applications
    \item \textbf{Code Quality:} Demonstrates best practices in secure coding and comprehensive testing
\end{enumerate}

\subsection{Learning Reflection}

The development process provided valuable insights into:

\begin{itemize}
    \item The complexity of implementing cryptographic systems correctly
    \item The importance of balancing security with usability
    \item The critical role of proper testing in security applications
    \item The challenges of secure software development
\end{itemize}

\subsection{Academic Impact}

This project contributes to the cybersecurity curriculum by:

\begin{itemize}
    \item Providing a practical example of cryptographic implementation
    \item Demonstrating the application of theoretical concepts in real software
    \item Offering a foundation for further research and development
    \item Serving as a reference for future cybersecurity projects
\end{itemize}

\subsection{Professional Development}

The project enhanced professional skills in:

\begin{itemize}
    \item Cryptographic programming and security implementation
    \item Java development and GUI design
    \item Software testing and quality assurance
    \item Technical documentation and academic writing
\end{itemize}

\subsection{Final Remarks}

The secure chat application successfully achieves its educational and technical objectives, providing a solid foundation for understanding secure communication systems. While the current implementation focuses on educational demonstration, the principles and techniques employed are directly applicable to production security systems.

The project demonstrates that with proper understanding of cryptographic principles and careful implementation, it is possible to create secure applications that protect user privacy while maintaining usability. This balance between security and usability represents one of the key challenges in modern cybersecurity, and this project provides valuable experience in addressing this challenge.

\section*{Acknowledgments}
\addcontentsline{toc}{section}{Acknowledgments}

I would like to express my sincere gratitude to Professor Boubakeur Annane for his guidance and supervision throughout this project. His expertise in cybersecurity and constructive feedback were instrumental in the successful completion of this work.

I also thank the Faculty of Technology at Ferhat Abbas University, Setif-1, for providing the educational environment and resources necessary for this research. The cybersecurity program has provided an excellent foundation for understanding the theoretical and practical aspects of information security.

Special thanks to my fellow students in the cybersecurity program for their collaboration and discussions that enriched my understanding of the subject matter.

\newpage

\begin{thebibliography}{99}

\bibitem{rsa1977}
Rivest, R. L., Shamir, A., \& Adleman, L. (1978). A method for obtaining digital signatures and public-key cryptosystems. \textit{Communications of the ACM}, 21(2), 120-126.

\bibitem{nist2016}
National Institute of Standards and Technology. (2016). \textit{NIST Special Publication 800-57 Part 1 Revision 4: Recommendation for Key Management}. U.S. Department of Commerce.

\bibitem{schneier2015}
Schneier, B. (2015). \textit{Applied Cryptography: Protocols, Algorithms and Source Code in C}. John Wiley \& Sons.

\bibitem{stallings2017}
Stallings, W. (2017). \textit{Cryptography and Network Security: Principles and Practice}. Pearson.

\bibitem{ferguson2010}
Ferguson, N., Schneier, B., \& Kohno, T. (2010). \textit{Cryptography Engineering: Design Principles and Practical Applications}. Wiley Publishing.

\bibitem{oracle2023}
Oracle Corporation. (2023). \textit{Java Cryptography Architecture (JCA) Reference Guide}. Oracle Documentation.

\bibitem{owasp2023}
OWASP Foundation. (2023). \textit{OWASP Top Ten Web Application Security Risks}. Retrieved from https://owasp.org/

\bibitem{katz2014}
Katz, J., \& Lindell, Y. (2014). \textit{Introduction to Modern Cryptography}. CRC Press.

\bibitem{menezes1996}
Menezes, A. J., Van Oorschot, P. C., \& Vanstone, S. A. (1996). \textit{Handbook of Applied Cryptography}. CRC Press.

\bibitem{anderson2008}
Anderson, R. (2008). \textit{Security Engineering: A Guide to Building Dependable Distributed Systems}. Wiley.

\end{thebibliography}

\newpage

\appendix

\section{Source Code Structure}

\subsection{Project Directory Structure}

\begin{lstlisting}[language=bash, caption=Complete Project Structure]
secure-chat-application/
├── src/
│   ├── main/java/com/securechat/
│   │   ├── controller/
│   │   │   └── SecureChatApplication.java
│   │   ├── model/
│   │   │   ├── User.java
│   │   │   ├── Message.java
│   │   │   ├── ParticipantListModel.java
│   │   │   ├── MessageListModel.java
│   │   │   └── CourseModel.java
│   │   ├── security/
│   │   │   ├── RSAUtil.java
│   │   │   └── MessageProxy.java
│   │   └── view/
│   │       ├── LoginView.java
│   │       ├── RegisterView.java
│   │       ├── MainAppView.java
│   │       ├── ChatView.java
│   │       ├── CourseView.java
│   │       └── ParticipantListView.java
│   └── test/java/com/securechat/
│       └── security/
│           └── RSAUtilTest.java
├── resources/
│   └── course-content.txt
├── docs/
│   ├── API.md
│   ├── SECURITY.md
│   └── screenshot_guide.md
├── pom.xml
├── README.md
├── LICENSE
└── .gitignore
\end{lstlisting}

\section{Installation and Setup Guide}

\subsection{System Requirements}

\begin{itemize}
    \item Java Development Kit (JDK) 8 or higher
    \item Maven 3.6 or higher (optional)
    \item Git for version control
    \item IDE (IntelliJ IDEA, Eclipse, or VS Code recommended)
\end{itemize}

\subsection{Installation Steps}

\begin{enumerate}
    \item Clone the repository:
    \begin{lstlisting}[language=bash]
git clone https://github.com/username/secure-chat-application.git
cd secure-chat-application
    \end{lstlisting}

    \item Compile the application:
    \begin{lstlisting}[language=bash]
# Using Maven
mvn clean compile

# Using command line
mkdir -p target/classes
javac -cp src/main/java -d target/classes src/main/java/com/securechat/*/*.java
    \end{lstlisting}

    \item Run the application:
    \begin{lstlisting}[language=bash]
# Using Maven
mvn exec:java -Dexec.mainClass="com.securechat.controller.SecureChatApplication"

# Using command line
java -cp target/classes com.securechat.controller.SecureChatApplication
    \end{lstlisting}
\end{enumerate}

\section{User Manual}

\subsection{Getting Started}

\begin{enumerate}
    \item Launch the application
    \item Use demo credentials or register a new user
    \item Access the main application interface
    \item Start secure messaging or view course materials
\end{enumerate}

\subsection{Demo User Accounts}

\begin{table}[H]
\centering
\begin{tabular}{|l|l|}
\hline
\textbf{Username} & \textbf{Password} \\
\hline
admin & admin123 \\
alice & password \\
bob & 123456 \\
charlie & secure \\
\hline
\end{tabular}
\caption{Pre-configured Demo Accounts}
\end{table}

\end{document}